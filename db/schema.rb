# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_05_29_125742) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "accounts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name"
    t.string "accountable_type", null: false
    t.bigint "accountable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["accountable_type", "accountable_id"], name: "index_accounts_on_accountable_type_and_accountable_id"
    t.index ["user_id"], name: "index_accounts_on_user_id"
  end

  create_table "accounts_scripts", id: false, force: :cascade do |t|
    t.bigint "script_id", null: false
    t.bigint "account_id", null: false
    t.index ["script_id"], name: "index_accounts_scripts_on_script_id"
  end

  create_table "ctrader_accounts", force: :cascade do |t|
    t.bigint "account_id"
    t.string "access_token"
    t.string "refresh_token"
    t.bigint "expires_in"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "host_type"
    t.bigint "account_number"
    t.boolean "auto_refresh_token", default: true
  end

  create_table "deals", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "position_id"
    t.string "accountable_type"
    t.bigint "accountable_id"
    t.string "symbol"
    t.float "volume"
    t.float "lot"
    t.integer "trade_side"
    t.integer "status"
    t.bigint "script_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id", "accountable_type", "accountable_id"], name: "idx_on_order_id_accountable_type_accountable_id_25e76d9a86"
    t.index ["position_id", "accountable_type", "accountable_id"], name: "idx_on_position_id_accountable_type_accountable_id_d65536e9fd"
    t.index ["script_id"], name: "index_deals_on_script_id"
  end

  create_table "known_mt5_servers", force: :cascade do |t|
    t.string "mt5_url"
    t.string "server_name", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "mt5_accounts", force: :cascade do |t|
    t.bigint "login"
    t.string "password"
    t.string "server"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "volume_leverage", default: 1.0
  end

  create_table "scripts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "from_account_id"
    t.string "type"
    t.text "config"
    t.index ["from_account_id"], name: "index_scripts_on_from_account_id"
    t.index ["user_id"], name: "index_scripts_on_user_id"
  end

  create_table "slave_deals", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "position_id"
    t.string "accountable_type"
    t.bigint "accountable_id"
    t.string "symbol"
    t.float "volume"
    t.float "lot"
    t.integer "trade_side"
    t.integer "status"
    t.bigint "script_id", null: false
    t.bigint "host_deal_id"
    t.text "pending_changes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["host_deal_id"], name: "index_slave_deals_on_host_deal_id"
    t.index ["order_id", "accountable_type", "accountable_id"], name: "idx_on_order_id_accountable_type_accountable_id_92a54e5036"
    t.index ["position_id", "accountable_type", "accountable_id"], name: "idx_on_position_id_accountable_type_accountable_id_2541a31dfb"
    t.index ["script_id"], name: "index_slave_deals_on_script_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false, null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "accounts", "users"
  add_foreign_key "deals", "scripts"
  add_foreign_key "scripts", "accounts", column: "from_account_id"
  add_foreign_key "scripts", "users"
  add_foreign_key "slave_deals", "deals", column: "host_deal_id"
  add_foreign_key "slave_deals", "scripts"
end
